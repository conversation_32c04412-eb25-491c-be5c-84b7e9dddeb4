apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-nest-backend
  namespace: ai-nest-backend
  labels:
    app: ai-nest-backend
    component: api
    version: v1.0.0
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-nest-backend
  template:
    metadata:
      labels:
        app: ai-nest-backend
        component: api
        version: v1.0.0
    spec:
      initContainers:
      - name: wait-for-postgres
        image: postgres:13
        command: ['sh', '-c']
        args:
        - |
          until pg_isready -h postgres-service -p 5432 -U postgres; do
            echo "Waiting for PostgreSQL to be ready..."
            sleep 2
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
      containers:
      - name: ai-nest-backend
        image: saipriya104/ai-nest-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        envFrom:
        - configMapRef:
            name: ai-nest-backend-config
        env:
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: DB_PASSWORD
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: JWT_SECRET
        - name: SMTP_USER
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: SMTP_USER
        - name: SMTP_PASS
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: SMTP_PASS
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: GOOGLE_CLIENT_ID
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: ai-nest-backend-secrets
              key: GOOGLE_CLIENT_SECRET
        livenessProbe:
          httpGet:
            path: /api/v1/oauth2/status
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/oauth2/status
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
      restartPolicy: Always