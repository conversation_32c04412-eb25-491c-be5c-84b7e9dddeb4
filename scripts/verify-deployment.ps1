# AI Nest Backend - Deployment Verification Script
# This script verifies the GitOps deployment status

Write-Host "🔍 AI Nest Backend - Deployment Verification" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Function to check pod status
function Check-PodStatus {
    param($namespace, $appName)
    
    Write-Host "`n📦 Checking pods in namespace: $namespace" -ForegroundColor Yellow
    try {
        $pods = kubectl get pods -n $namespace --no-headers 2>$null
        if ($pods) {
            kubectl get pods -n $namespace
            
            # Check if all pods are running
            $runningPods = kubectl get pods -n $namespace --field-selector=status.phase=Running --no-headers 2>$null
            $totalPods = ($pods -split "`n").Count
            $runningCount = if ($runningPods) { ($runningPods -split "`n").Count } else { 0 }
            
            Write-Host "✅ Running pods: $runningCount/$totalPods" -ForegroundColor Green
        } else {
            Write-Host "❌ No pods found in namespace $namespace" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error checking pods in namespace $namespace" -ForegroundColor Red
    }
}

# Function to check service status
function Check-ServiceStatus {
    param($namespace)
    
    Write-Host "`n🌐 Checking services in namespace: $namespace" -ForegroundColor Yellow
    try {
        $services = kubectl get svc -n $namespace --no-headers 2>$null
        if ($services) {
            kubectl get svc -n $namespace
        } else {
            Write-Host "❌ No services found in namespace $namespace" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error checking services in namespace $namespace" -ForegroundColor Red
    }
}

# Function to check Argo CD application status
function Check-ArgoApplication {
    Write-Host "`n🎯 Checking Argo CD Application Status..." -ForegroundColor Yellow
    try {
        $appExists = kubectl get application ai-nest-backend -n argocd --no-headers 2>$null
        if ($appExists) {
            Write-Host "✅ Argo CD Application exists" -ForegroundColor Green
            
            # Get detailed status
            $syncStatus = kubectl get application ai-nest-backend -n argocd -o jsonpath='{.status.sync.status}' 2>$null
            $healthStatus = kubectl get application ai-nest-backend -n argocd -o jsonpath='{.status.health.status}' 2>$null
            
            Write-Host "📊 Sync Status: $syncStatus" -ForegroundColor Cyan
            Write-Host "🏥 Health Status: $healthStatus" -ForegroundColor Cyan
            
            # Show full application status
            kubectl get application ai-nest-backend -n argocd
        } else {
            Write-Host "❌ Argo CD Application not found" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error checking Argo CD Application" -ForegroundColor Red
    }
}

# Function to test application endpoints
function Test-ApplicationEndpoints {
    Write-Host "`n🔗 Testing Application Endpoints..." -ForegroundColor Yellow
    
    # Get service URL
    try {
        $serviceType = kubectl get svc ai-nest-backend-service -n ai-nest-backend -o jsonpath='{.spec.type}' 2>$null
        
        if ($serviceType -eq "NodePort") {
            $nodePort = kubectl get svc ai-nest-backend-service -n ai-nest-backend -o jsonpath='{.spec.ports[0].nodePort}' 2>$null
            $minikubeIP = minikube ip 2>$null
            
            if ($minikubeIP -and $nodePort) {
                $baseUrl = "http://$minikubeIP`:$nodePort"
                Write-Host "🌐 Service URL: $baseUrl" -ForegroundColor Cyan
                
                # Test health endpoint
                try {
                    $response = Invoke-WebRequest -Uri "$baseUrl/api/v1/oauth2/status" -TimeoutSec 10 -UseBasicParsing 2>$null
                    if ($response.StatusCode -eq 200) {
                        Write-Host "✅ Health check endpoint responding" -ForegroundColor Green
                    }
                } catch {
                    Write-Host "❌ Health check endpoint not responding" -ForegroundColor Red
                }
                
                # Test API documentation
                try {
                    $response = Invoke-WebRequest -Uri "$baseUrl/api-docs" -TimeoutSec 10 -UseBasicParsing 2>$null
                    if ($response.StatusCode -eq 200) {
                        Write-Host "✅ API documentation accessible" -ForegroundColor Green
                        Write-Host "📚 Swagger UI: $baseUrl/api-docs" -ForegroundColor Cyan
                    }
                } catch {
                    Write-Host "⚠️  API documentation not yet accessible" -ForegroundColor Yellow
                }
            }
        } else {
            Write-Host "⚠️  Service is not NodePort type. Use port-forward for testing:" -ForegroundColor Yellow
            Write-Host "   kubectl port-forward svc/ai-nest-backend-service -n ai-nest-backend 8080:8080" -ForegroundColor White
        }
    } catch {
        Write-Host "❌ Error testing application endpoints" -ForegroundColor Red
    }
}

# Function to check database connectivity
function Check-DatabaseConnectivity {
    Write-Host "`n🗄️  Checking Database Connectivity..." -ForegroundColor Yellow
    try {
        $postgresRunning = kubectl get pods -n ai-nest-backend -l app=postgres --field-selector=status.phase=Running --no-headers 2>$null
        if ($postgresRunning) {
            Write-Host "✅ PostgreSQL pod is running" -ForegroundColor Green
            
            # Test database connection
            $testResult = kubectl exec -n ai-nest-backend deployment/postgres -- pg_isready -U postgres 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Database is accepting connections" -ForegroundColor Green
            } else {
                Write-Host "❌ Database is not accepting connections" -ForegroundColor Red
            }
        } else {
            Write-Host "❌ PostgreSQL pod is not running" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ Error checking database connectivity" -ForegroundColor Red
    }
}

# Main verification process
Write-Host "`n🚀 Starting deployment verification..." -ForegroundColor Green

# Check Argo CD Application
Check-ArgoApplication

# Check application namespace
Check-PodStatus -namespace "ai-nest-backend" -appName "ai-nest-backend"
Check-ServiceStatus -namespace "ai-nest-backend"

# Check database connectivity
Check-DatabaseConnectivity

# Test application endpoints
Test-ApplicationEndpoints

# Summary
Write-Host "`n📋 Verification Summary" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green

$namespaceExists = kubectl get namespace ai-nest-backend --no-headers 2>$null
$appRunning = kubectl get pods -n ai-nest-backend -l app=ai-nest-backend --field-selector=status.phase=Running --no-headers 2>$null
$dbRunning = kubectl get pods -n ai-nest-backend -l app=postgres --field-selector=status.phase=Running --no-headers 2>$null

if ($namespaceExists) { Write-Host "✅ Namespace created" -ForegroundColor Green } else { Write-Host "❌ Namespace missing" -ForegroundColor Red }
if ($appRunning) { Write-Host "✅ Application running" -ForegroundColor Green } else { Write-Host "❌ Application not running" -ForegroundColor Red }
if ($dbRunning) { Write-Host "✅ Database running" -ForegroundColor Green } else { Write-Host "❌ Database not running" -ForegroundColor Red }

Write-Host "`n🎉 Verification completed!" -ForegroundColor Green
